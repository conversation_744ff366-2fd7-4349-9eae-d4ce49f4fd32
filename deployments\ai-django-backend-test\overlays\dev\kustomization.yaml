apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-django-backend-test-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels


labels:
- pairs:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/version: "91adc17a"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-ai-django-backend
    source.branch: 15-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-django-backend-test


- path: init-container-patch.yaml
  target:
    kind: Deployment
    name: ai-django-backend-test



namePrefix: ""
nameSuffix: "-dev"
