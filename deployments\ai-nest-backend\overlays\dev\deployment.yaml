apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/version: "47db9e43"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend
  template:
    metadata:
      labels:
        app: ai-nest-backend
        app.kubernetes.io/name: ai-nest-backend
        app.kubernetes.io/component: nest-backend
        app.kubernetes.io/part-of: ai-nest-backend
        app.kubernetes.io/version: "47db9e43"
    spec:
      containers:
      - name: ai-nest-backend
        image: registry.digitalocean.com/doks-registry/ai-nest-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        envFrom:
        - configMapRef:
            name: ai-nest-backend-config
       
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: JWT_SECRET
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SESSION_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_SSL_MODE
        
        
        
        # NestJS/TypeORM specific database configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DATABASE_URL

        - name: TYPEORM_CONNECTION
          value: "postgres"
        - name: TYPEORM_HOST
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_HOST
        - name: TYPEORM_PORT
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PORT
        - name: TYPEORM_USERNAME
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_USER
        - name: TYPEORM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: TYPEORM_DATABASE
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_NAME
        
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        
        
        
        # NestJS Health Checks (Development - faster startup)
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
